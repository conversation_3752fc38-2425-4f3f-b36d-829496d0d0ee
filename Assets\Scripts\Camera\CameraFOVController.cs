using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

/// <summary>
/// 增强版相机FOV控制器
/// 用于在氮气加速时增大FOV，增强加速感，并添加多种视觉效果
/// </summary>
public class CameraFOVController : MonoBehaviour
{
    [Header("FOV设置")]
    [Tooltip("默认FOV值")]
    [SerializeField] private float defaultFOV = 60f;

    [Tooltip("氮气加速时的最大FOV值")]
    [SerializeField] private float nitroFOV = 75f;

    [Tooltip("FOV达到目标值所需的平滑时间（秒）")]
    [SerializeField] private float fovSmoothTime = 0.3f;

    [Header("镜头畸变效果")]
    [Tooltip("启用镜头畸变效果")]
    [SerializeField] private bool enableLensDistortion = true;

    [Tooltip("氮气时的最大镜头畸变强度")]
    [SerializeField] private float maxLensDistortion = -0.3f;

    [Tooltip("镜头畸变过渡时间")]
    [SerializeField] private float lensDistortionSmoothTime = 0.5f;

    [Header("色彩增强效果")]
    [Tooltip("启用色彩增强")]
    [SerializeField] private bool enableColorEnhancement = true;

    [Tooltip("氮气时的色彩饱和度增强")]
    [SerializeField] private float nitroSaturationBoost = 1.3f;

    [Tooltip("氮气时的对比度增强")]
    [SerializeField] private float nitroContrastBoost = 1.2f;

    [Header("屏幕震动效果")]
    [Tooltip("启用屏幕震动")]
    [SerializeField] private bool enableScreenShake = true;

    [Tooltip("氮气启动时的震动强度")]
    [SerializeField] private float nitroShakeIntensity = 0.1f;

    [Tooltip("震动持续时间")]
    [SerializeField] private float shakeDuration = 0.3f;



    [Header("引用")]
    [Tooltip("后处理Volume组件（用于镜头畸变和色彩调整）")]
    [SerializeField] private Volume postProcessVolume;

    private CarController targetCarController; // 改为私有

    // 公共属性
    public float DefaultFOV => defaultFOV;
    public float NitroFOV => nitroFOV;
    public float CurrentFOV => cameraComponent ? cameraComponent.fieldOfView : defaultFOV;

    // 相机组件
    private Camera cameraComponent;

    // 目标FOV值
    private float targetFOVForSmoothing;
    private float velocityFOV = 0f;

    // 后处理效果组件
    private LensDistortion lensDistortion;
    private ColorAdjustments colorAdjustments;

    // 镜头畸变相关变量
    private float currentLensDistortion = 0f;
    private float targetLensDistortion = 0f;
    private float lensDistortionVelocity = 0f;

    // 色彩调整相关变量
    private float defaultSaturation = 0f;
    private float defaultContrast = 0f;
    private float currentSaturation = 0f;
    private float currentContrast = 0f;

    // 屏幕震动相关变量
    private Vector3 originalCameraPosition;
    private float shakeTimer = 0f;
    private bool isShaking = false;
    private bool wasNitroActive = false;


    private void Awake()
    {
        // 获取相机组件
        cameraComponent = GetComponent<Camera>();
        if (cameraComponent == null)
        {
            Debug.LogError("CameraFOVController需要挂载在带有Camera组件的游戏对象上！");
            enabled = false;
            return;
        }

        // 设置初始FOV
        cameraComponent.fieldOfView = defaultFOV;
        targetFOVForSmoothing = defaultFOV;

        // 保存原始相机位置
        originalCameraPosition = transform.localPosition;

        // 初始化后处理效果
        InitializePostProcessing();

        // 自动获取 "Player" 标签的车辆的 CarController
        GameObject playerObject = GameObject.FindGameObjectWithTag("Player");
        if (playerObject != null)
        {
            targetCarController = playerObject.GetComponent<CarController>();
            if (targetCarController == null)
            {
                Debug.LogWarning("CameraFOVController: 在带有 'Player' 标签的对象上未找到 CarController 组件! ", this);
            }
        }
        else
        {
            Debug.LogWarning("CameraFOVController: 未在场景中找到带有 'Player' 标签的游戏对象!", this);
        }
    }

    private void InitializePostProcessing()
    {
        // 如果没有手动分配Volume，尝试自动查找
        if (postProcessVolume == null)
        {
            postProcessVolume = FindObjectOfType<Volume>();
        }

        if (postProcessVolume != null && postProcessVolume.profile != null)
        {
            // 获取镜头畸变组件
            if (enableLensDistortion && postProcessVolume.profile.TryGet<LensDistortion>(out lensDistortion))
            {
                lensDistortion.active = true;
                currentLensDistortion = lensDistortion.intensity.value;
                targetLensDistortion = currentLensDistortion;
            }

            // 获取色彩调整组件
            if (enableColorEnhancement && postProcessVolume.profile.TryGet<ColorAdjustments>(out colorAdjustments))
            {
                colorAdjustments.active = true;
                defaultSaturation = colorAdjustments.saturation.value;
                defaultContrast = colorAdjustments.contrast.value;
                currentSaturation = defaultSaturation;
                currentContrast = defaultContrast;
            }
        }
        else if (enableLensDistortion || enableColorEnhancement)
        {
            Debug.LogWarning("CameraFOVController: 未找到后处理Volume组件，镜头畸变和色彩增强效果将无法使用！");
        }
    }

    private void Update()
    {
        if (cameraComponent == null) return;

        float calculatedTargetFOV = defaultFOV;
        bool isNitroActive = false;
        float speedFactor = 0f;

        if (targetCarController != null && targetCarController.IsNitroSystemEnabled)
        {
            isNitroActive = targetCarController.IsNitroActiveAndEnabled;
            float currentSpeedMS = targetCarController.GetCurrentForwardSpeedMS();
            float maxSpeedKPH = targetCarController.TargetEngineSpeedKPH;
            float maxSpeedMS = maxSpeedKPH / 3.6f;

            if (maxSpeedMS > 0.01f)
            {
                speedFactor = Mathf.Clamp01(currentSpeedMS / maxSpeedMS);
            }

            // 根据氮气状态和速度设置目标FOV
            if (isNitroActive)
            {
                // 根据速度因子计算FOV增量，速度越快FOV增加越多
                float speedFactorCurved = speedFactor * speedFactor;
                float fovIncrease = Mathf.Lerp(0, nitroFOV - defaultFOV, speedFactorCurved);
                calculatedTargetFOV = defaultFOV + fovIncrease;
            }
            else
            {
                // 当速度超过最大速度的70%时，也稍微增加FOV
                if (speedFactor > 0.7f)
                {
                    float highSpeedFactor = (speedFactor - 0.7f) / 0.3f; // Normalize 0.7-1.0 range to 0-1
                    float highSpeedFactorCurved = highSpeedFactor * highSpeedFactor;
                    float highSpeedFovIncrease = Mathf.Lerp(0, (nitroFOV - defaultFOV) * 0.4f, highSpeedFactorCurved); // Max 40% of nitro FOV increase
                    calculatedTargetFOV = defaultFOV + highSpeedFovIncrease;
                }
                else
                {
                    calculatedTargetFOV = defaultFOV;
                }
            }
        }
        else
        {
            calculatedTargetFOV = defaultFOV;
        }

        // 使用SmoothDamp平滑FOV变化
        cameraComponent.fieldOfView = Mathf.SmoothDamp(cameraComponent.fieldOfView, calculatedTargetFOV, ref velocityFOV, fovSmoothTime);

        // 更新增强视觉效果
        UpdateLensDistortion(isNitroActive, speedFactor);
        UpdateColorEnhancement(isNitroActive, speedFactor);
        UpdateScreenShake(isNitroActive);
    }

    /// <summary>
    /// 更新镜头畸变效果
    /// </summary>
    private void UpdateLensDistortion(bool isNitroActive, float speedFactor)
    {
        if (!enableLensDistortion || lensDistortion == null) return;

        // 计算目标畸变强度
        if (isNitroActive)
        {
            // 氮气激活时，根据速度因子应用畸变
            float distortionIntensity = Mathf.Lerp(0f, maxLensDistortion, speedFactor);
            targetLensDistortion = distortionIntensity;
        }
        else
        {
            // 氮气未激活时，逐渐恢复到默认值
            targetLensDistortion = 0f;
        }

        // 平滑过渡到目标畸变值
        currentLensDistortion = Mathf.SmoothDamp(currentLensDistortion, targetLensDistortion,
            ref lensDistortionVelocity, lensDistortionSmoothTime);

        // 应用畸变效果
        lensDistortion.intensity.value = currentLensDistortion;
    }

    /// <summary>
    /// 更新色彩增强效果
    /// </summary>
    private void UpdateColorEnhancement(bool isNitroActive, float speedFactor)
    {
        if (!enableColorEnhancement || colorAdjustments == null) return;

        float targetSaturation, targetContrast;

        if (isNitroActive)
        {
            // 氮气激活时，增强色彩饱和度和对比度
            float enhancementFactor = Mathf.Lerp(0f, 1f, speedFactor);
            targetSaturation = Mathf.Lerp(defaultSaturation, (nitroSaturationBoost - 1f) * 100f, enhancementFactor);
            targetContrast = Mathf.Lerp(defaultContrast, (nitroContrastBoost - 1f) * 100f, enhancementFactor);
        }
        else
        {
            // 氮气未激活时，恢复到默认值
            targetSaturation = defaultSaturation;
            targetContrast = defaultContrast;
        }

        // 平滑过渡
        currentSaturation = Mathf.Lerp(currentSaturation, targetSaturation, Time.deltaTime * 2f);
        currentContrast = Mathf.Lerp(currentContrast, targetContrast, Time.deltaTime * 2f);

        // 应用色彩调整
        colorAdjustments.saturation.value = currentSaturation;
        colorAdjustments.contrast.value = currentContrast;
    }

    /// <summary>
    /// 更新屏幕震动效果
    /// </summary>
    private void UpdateScreenShake(bool isNitroActive)
    {
        if (!enableScreenShake) return;

        // 检测氮气激活状态变化
        if (isNitroActive && !wasNitroActive)
        {
            // 氮气刚激活，开始震动
            StartScreenShake();
        }

        wasNitroActive = isNitroActive;

        // 更新震动效果
        if (isShaking)
        {
            shakeTimer -= Time.deltaTime;

            if (shakeTimer <= 0f)
            {
                // 震动结束，恢复原始位置
                isShaking = false;
                transform.localPosition = originalCameraPosition;
            }
            else
            {
                // 计算震动偏移
                float shakeIntensity = Mathf.Lerp(nitroShakeIntensity, 0f, 1f - (shakeTimer / shakeDuration));
                Vector3 shakeOffset = new Vector3(
                    Random.Range(-shakeIntensity, shakeIntensity),
                    Random.Range(-shakeIntensity, shakeIntensity),
                    0f
                );

                transform.localPosition = originalCameraPosition + shakeOffset;
            }
        }
    }

    /// <summary>
    /// 开始屏幕震动
    /// </summary>
    private void StartScreenShake()
    {
        isShaking = true;
        shakeTimer = shakeDuration;
    }
}