<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 9.0-c001 152.deb9585, 2024/02/06-08:36:10        " rdfhash="532022C3478674AC34DE88E176920C99" merged="0">
 <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
  <rdf:Description rdf:about=""
    xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
    xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#"
    xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"
    xmlns:xmp="http://ns.adobe.com/xap/1.0/"
    xmlns:xmpDM="http://ns.adobe.com/xmp/1.0/DynamicMedia/"
    xmlns:riffinfo="http://ns.adobe.com/riff/info/"
    xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmpMM:InstanceID="xmp.iid:eda6a89e-2018-124c-a71b-5909401687d0"
   xmpMM:DocumentID="1504f99f-9da9-a5ae-adf2-b3f800000057"
   xmpMM:OriginalDocumentID="xmp.did:787fb55d-20e6-d043-a567-54706c93ef47"
   xmp:MetadataDate="2025-05-20T19:27:27+08:00"
   xmp:ModifyDate="2025-05-20T19:27:27+08:00"
   xmp:CreatorTool="Lavf61.7.100"
   xmp:CreateDate="2025-05-20T19:26:54+08:00"
   xmpDM:artist="Pillarz"
   xmpDM:logComment="163 key(Don't modify):L64FU3W4YxX3ZFTmbZ+8/XkMRvbZsXI4JXvblz1DE/UODL3c54pS4KU82T+1z7JGv5ftLh+qMT4Tn3igjzx1Tr95rUwuaH9h5isQ0DnKzTKgLCunM6mjkhW/ZQu4h8Ip5aKxJ7bb1ViKEGmHEAR7tejdsmqW3GjASsxznDx88WFhX2n9Mm4mNaWm3LARjM9y/acG3/HbD/mdQytAQQT5bSAYTWKW782B88xxKErjU6maf77tG6bCwH/EMLTTGDs0B5fMXOO7143RR15ENaMEIlyYCa6unPureQ6UmoTjwsD+sFHZ1bos01mdt+SJjn67fQ2Sj6KUXqMrqxW3aUp+0Mj7KinSn7JvAQ7ydFeqeqV6rCd0TyzzWah/iOaHQtweHaKc94IsDKlw8QG5RnRxOeKL3C4b60m1gL3DtAwsYQD0NTRMiLb+ROYydhqjJzKIQMrvWwLSd0DsluIQErLgF22d6mk+5joa7pefLlnOtRmXgQ/ir/i5bePZEU5mdioUbUyzuWh1lS/HfBc+ti9CxilheeR/QQ6zh+ai8tMUtALQN/luNuQd6hmHuLrPN+IP"
   xmpDM:audioSampleRate="48000"
   xmpDM:audioSampleType="16Int"
   xmpDM:audioChannelType="Stereo"
   xmpDM:startTimeScale="30000"
   xmpDM:startTimeSampleSize="1001"
   riffinfo:product="Infraction"
   dc:format="AAC 音频">
   <xmpMM:History>
    <rdf:Seq>
     <rdf:li
      stEvt:action="saved"
      stEvt:instanceID="a09a28ab-0559-9d9e-46b6-7ea500000084"
      stEvt:when="2025-05-20T19:27:27+08:00"
      stEvt:softwareAgent="Adobe Adobe Media Encoder 2024.0 (Windows)"
      stEvt:changed="/"/>
     <rdf:li
      stEvt:action="saved"
      stEvt:instanceID="e038e777-3228-8ab2-9028-219b00000084"
      stEvt:when="2025-05-20T19:27:08+08:00"
      stEvt:softwareAgent="Adobe Adobe Media Encoder 2024.0 (Windows)"
      stEvt:changed="/"/>
     <rdf:li
      stEvt:action="saved"
      stEvt:instanceID="xmp.iid:2403d2ac-5bed-7f45-a211-6d88e7edc815"
      stEvt:when="2025-05-20T19:27:27+08:00"
      stEvt:softwareAgent="Adobe Adobe Media Encoder 2024.0 (Windows)"
      stEvt:changed="/"/>
     <rdf:li
      stEvt:action="saved"
      stEvt:instanceID="xmp.iid:eda6a89e-2018-124c-a71b-5909401687d0"
      stEvt:when="2025-05-20T19:27:27+08:00"
      stEvt:softwareAgent="Adobe Adobe Media Encoder 2024.0 (Windows)"
      stEvt:changed="/metadata"/>
    </rdf:Seq>
   </xmpMM:History>
   <xmpMM:DerivedFrom
    stRef:instanceID="e038e777-3228-8ab2-9028-219b00000084"
    stRef:documentID="b34f583b-da9b-0794-7c45-8a2600000057"
    stRef:originalDocumentID="xmp.did:19371b53-3cf7-174e-bf2d-2a0184d45f58"/>
   <xmpDM:startTimecode
    xmpDM:timeFormat="AudioSamplesTimecode"
    xmpDM:timeValue="00:00:00:00000"/>
   <xmpDM:duration
    xmpDM:value="11865443"
    xmpDM:scale="1/48000"/>
   <xmpDM:altTimecode
    xmpDM:timeValue="00:00:00:00000"
    xmpDM:timeFormat="AudioSamplesTimecode"/>
   <riffinfo:name>
    <rdf:Alt>
     <rdf:li xml:lang="x-default">Infraction</rdf:li>
    </rdf:Alt>
   </riffinfo:name>
   <dc:title>
    <rdf:Alt>
     <rdf:li xml:lang="x-default">Infraction</rdf:li>
    </rdf:Alt>
   </dc:title>
  </rdf:Description>
 </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?>