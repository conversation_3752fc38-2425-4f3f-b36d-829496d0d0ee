using UnityEngine;

/// <summary>
/// 速度线条效果管理器
/// 在高速行驶时在相机周围生成速度线条，增强速度感
/// </summary>
public class SpeedLinesEffect : MonoBehaviour
{
    [Header("速度线条设置")]
    [Tooltip("速度线条粒子系统预制体")]
    [SerializeField] private GameObject speedLinesPrefab;
    
    [Tooltip("激活速度线条的最小速度 (km/h)")]
    [SerializeField] private float minSpeedForLines = 80f;
    
    [Tooltip("最大速度线条强度对应的速度 (km/h)")]
    [SerializeField] private float maxSpeedForLines = 150f;
    
    [Tooltip("速度线条最大发射率")]
    [SerializeField] private float maxEmissionRate = 100f;
    
    [Tooltip("速度线条最小发射率")]
    [SerializeField] private float minEmissionRate = 20f;

    [Header("氮气增强效果")]
    [Tooltip("氮气激活时的线条强度倍数")]
    [SerializeField] private float nitroIntensityMultiplier = 2.0f;
    
    [Tooltip("氮气激活时的线条颜色")]
    [SerializeField] private Color nitroLineColor = Color.cyan;
    
    [Tooltip("默认线条颜色")]
    [SerializeField] private Color defaultLineColor = Color.white;

    [Header("环境适应")]
    [Tooltip("根据环境光调整线条透明度")]
    [SerializeField] private bool adaptToEnvironment = true;
    
    [Tooltip("夜晚线条透明度倍数")]
    [SerializeField] private float nightAlphaMultiplier = 1.5f;
    
    [Tooltip("白天线条透明度倍数")]
    [SerializeField] private float dayAlphaMultiplier = 0.7f;

    [Header("引用")]
    [Tooltip("车辆控制器")]
    [SerializeField] private CarController carController;
    
    [Tooltip("相机组件")]
    [SerializeField] private Camera targetCamera;

    // 私有变量
    private ParticleSystem speedLinesParticles;
    private ParticleSystem.EmissionModule emissionModule;
    private ParticleSystem.MainModule mainModule;
    private bool isEffectActive = false;
    private float currentEmissionRate = 0f;

    private void Awake()
    {
        // 自动获取组件
        if (carController == null)
        {
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                carController = player.GetComponent<CarController>();
            }
        }

        if (targetCamera == null)
        {
            targetCamera = GetComponent<Camera>();
            if (targetCamera == null)
            {
                targetCamera = Camera.main;
            }
        }

        // 初始化速度线条
        InitializeSpeedLines();
    }

    private void InitializeSpeedLines()
    {
        if (speedLinesPrefab != null)
        {
            // 创建速度线条粒子系统
            GameObject speedLinesObject = Instantiate(speedLinesPrefab, transform);
            speedLinesParticles = speedLinesObject.GetComponent<ParticleSystem>();

            if (speedLinesParticles != null)
            {
                emissionModule = speedLinesParticles.emission;
                mainModule = speedLinesParticles.main;
                
                // 初始状态：关闭发射
                emissionModule.enabled = false;
                speedLinesParticles.Stop();
            }
            else
            {
                Debug.LogWarning("SpeedLinesEffect: 速度线条预制体没有ParticleSystem组件！");
            }
        }
        else
        {
            Debug.LogWarning("SpeedLinesEffect: 未分配速度线条预制体！");
        }
    }

    private void Update()
    {
        if (carController == null || speedLinesParticles == null) return;

        // 获取当前速度
        float currentSpeedKMH = carController.GetCurrentForwardSpeedMS() * 3.6f;
        bool isNitroActive = carController.IsNitroActiveAndEnabled;

        // 更新速度线条效果
        UpdateSpeedLinesEffect(currentSpeedKMH, isNitroActive);
    }

    /// <summary>
    /// 更新速度线条效果
    /// </summary>
    private void UpdateSpeedLinesEffect(float currentSpeed, bool isNitroActive)
    {
        bool shouldShowLines = currentSpeed >= minSpeedForLines;

        if (shouldShowLines && !isEffectActive)
        {
            // 开始显示速度线条
            StartSpeedLines();
        }
        else if (!shouldShowLines && isEffectActive)
        {
            // 停止显示速度线条
            StopSpeedLines();
        }

        if (isEffectActive)
        {
            // 更新线条强度和颜色
            UpdateLineIntensity(currentSpeed, isNitroActive);
            UpdateLineColor(isNitroActive);
            UpdateEnvironmentAdaptation();
        }
    }

    /// <summary>
    /// 开始速度线条效果
    /// </summary>
    private void StartSpeedLines()
    {
        if (speedLinesParticles != null)
        {
            emissionModule.enabled = true;
            speedLinesParticles.Play();
            isEffectActive = true;
        }
    }

    /// <summary>
    /// 停止速度线条效果
    /// </summary>
    private void StopSpeedLines()
    {
        if (speedLinesParticles != null)
        {
            emissionModule.enabled = false;
            // 不立即停止，让现有粒子自然消失
            isEffectActive = false;
        }
    }

    /// <summary>
    /// 更新线条强度
    /// </summary>
    private void UpdateLineIntensity(float currentSpeed, bool isNitroActive)
    {
        // 计算基础强度
        float speedFactor = Mathf.Clamp01((currentSpeed - minSpeedForLines) / (maxSpeedForLines - minSpeedForLines));
        float baseEmissionRate = Mathf.Lerp(minEmissionRate, maxEmissionRate, speedFactor);

        // 氮气增强
        if (isNitroActive)
        {
            baseEmissionRate *= nitroIntensityMultiplier;
        }

        // 平滑过渡
        currentEmissionRate = Mathf.Lerp(currentEmissionRate, baseEmissionRate, Time.deltaTime * 3f);
        emissionModule.rateOverTime = currentEmissionRate;

        // 更新粒子速度
        var velocityOverLifetime = speedLinesParticles.velocityOverLifetime;
        velocityOverLifetime.enabled = true;
        velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
        
        float particleSpeed = Mathf.Lerp(10f, 30f, speedFactor);
        if (isNitroActive)
        {
            particleSpeed *= 1.5f;
        }
        
        velocityOverLifetime.radial = new ParticleSystem.MinMaxCurve(-particleSpeed);
    }

    /// <summary>
    /// 更新线条颜色
    /// </summary>
    private void UpdateLineColor(bool isNitroActive)
    {
        Color targetColor = isNitroActive ? nitroLineColor : defaultLineColor;
        
        // 平滑过渡颜色
        Color currentColor = Color.Lerp(mainModule.startColor.color, targetColor, Time.deltaTime * 2f);
        mainModule.startColor = currentColor;
    }

    /// <summary>
    /// 根据环境光调整效果
    /// </summary>
    private void UpdateEnvironmentAdaptation()
    {
        if (!adaptToEnvironment) return;

        // 获取环境光强度
        float ambientIntensity = RenderSettings.ambientIntensity;
        
        // 根据环境光调整透明度
        float alphaMultiplier = ambientIntensity < 0.5f ? nightAlphaMultiplier : dayAlphaMultiplier;
        
        Color currentColor = mainModule.startColor.color;
        currentColor.a = Mathf.Clamp01(currentColor.a * alphaMultiplier);
        mainModule.startColor = currentColor;
    }

    /// <summary>
    /// 手动设置速度线条强度（用于外部调用）
    /// </summary>
    public void SetSpeedLinesIntensity(float intensity)
    {
        if (speedLinesParticles != null && isEffectActive)
        {
            intensity = Mathf.Clamp01(intensity);
            float targetEmissionRate = Mathf.Lerp(minEmissionRate, maxEmissionRate, intensity);
            emissionModule.rateOverTime = targetEmissionRate;
        }
    }

    /// <summary>
    /// 手动设置速度线条颜色（用于外部调用）
    /// </summary>
    public void SetSpeedLinesColor(Color color)
    {
        if (speedLinesParticles != null)
        {
            mainModule.startColor = color;
        }
    }

    /// <summary>
    /// 强制开启/关闭速度线条（用于外部调用）
    /// </summary>
    public void ForceSpeedLines(bool enable)
    {
        if (enable && !isEffectActive)
        {
            StartSpeedLines();
        }
        else if (!enable && isEffectActive)
        {
            StopSpeedLines();
        }
    }

    private void OnDestroy()
    {
        if (speedLinesParticles != null)
        {
            Destroy(speedLinesParticles.gameObject);
        }
    }

    private void OnValidate()
    {
        // 确保参数合理
        minSpeedForLines = Mathf.Max(0f, minSpeedForLines);
        maxSpeedForLines = Mathf.Max(minSpeedForLines + 10f, maxSpeedForLines);
        maxEmissionRate = Mathf.Max(minEmissionRate, maxEmissionRate);
        nitroIntensityMultiplier = Mathf.Max(1f, nitroIntensityMultiplier);
    }
}
