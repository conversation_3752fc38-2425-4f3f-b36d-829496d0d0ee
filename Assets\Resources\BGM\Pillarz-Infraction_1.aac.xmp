<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 9.0-c001 152.deb9585, 2024/02/06-08:36:10        " rdfhash="1C2E17D9DD5F7CF1A827151823684078" merged="0">
 <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
  <rdf:Description rdf:about=""
    xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
    xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#"
    xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"
    xmlns:xmp="http://ns.adobe.com/xap/1.0/"
    xmlns:xmpDM="http://ns.adobe.com/xmp/1.0/DynamicMedia/"
    xmlns:riffinfo="http://ns.adobe.com/riff/info/"
    xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmpMM:InstanceID="xmp.iid:11d7535f-5f1a-0c4f-b9d9-1a85956e9278"
   xmpMM:DocumentID="81d341e6-4828-036f-666b-f8d10000005b"
   xmpMM:OriginalDocumentID="xmp.did:aa86ea92-c0c4-a445-890b-74602bee3154"
   xmp:MetadataDate="2025-05-20T19:28:21+08:00"
   xmp:ModifyDate="2025-05-20T19:28:21+08:00"
   xmp:CreatorTool="Lavf61.7.100"
   xmp:CreateDate="2025-05-20T19:27:49+08:00"
   xmpDM:artist="Pillarz"
   xmpDM:logComment="163 key(Don't modify):L64FU3W4YxX3ZFTmbZ+8/XkMRvbZsXI4JXvblz1DE/UODL3c54pS4KU82T+1z7JGv5ftLh+qMT4Tn3igjzx1Tr95rUwuaH9h5isQ0DnKzTKgLCunM6mjkhW/ZQu4h8Ip5aKxJ7bb1ViKEGmHEAR7tejdsmqW3GjASsxznDx88WFhX2n9Mm4mNaWm3LARjM9y/acG3/HbD/mdQytAQQT5bSAYTWKW782B88xxKErjU6maf77tG6bCwH/EMLTTGDs0B5fMXOO7143RR15ENaMEIlyYCa6unPureQ6UmoTjwsD+sFHZ1bos01mdt+SJjn67fQ2Sj6KUXqMrqxW3aUp+0Mj7KinSn7JvAQ7ydFeqeqV6rCd0TyzzWah/iOaHQtweHaKc94IsDKlw8QG5RnRxOeKL3C4b60m1gL3DtAwsYQD0NTRMiLb+ROYydhqjJzKIQMrvWwLSd0DsluIQErLgF22d6mk+5joa7pefLlnOtRmXgQ/ir/i5bePZEU5mdioUbUyzuWh1lS/HfBc+ti9CxilheeR/QQ6zh+ai8tMUtALQN/luNuQd6hmHuLrPN+IP"
   xmpDM:audioSampleRate="48000"
   xmpDM:audioSampleType="16Int"
   xmpDM:audioChannelType="Stereo"
   xmpDM:startTimeScale="30000"
   xmpDM:startTimeSampleSize="1001"
   riffinfo:product="Infraction"
   dc:format="AAC 音频">
   <xmpMM:History>
    <rdf:Seq>
     <rdf:li
      stEvt:action="saved"
      stEvt:instanceID="cf836dd8-40f2-a26c-03e3-2f5b00000088"
      stEvt:when="2025-05-20T19:28:21+08:00"
      stEvt:softwareAgent="Adobe Adobe Media Encoder 2024.0 (Windows)"
      stEvt:changed="/"/>
     <rdf:li
      stEvt:action="saved"
      stEvt:instanceID="e038e777-3228-8ab2-9028-219b00000084"
      stEvt:when="2025-05-20T19:27:08+08:00"
      stEvt:softwareAgent="Adobe Adobe Media Encoder 2024.0 (Windows)"
      stEvt:changed="/"/>
     <rdf:li
      stEvt:action="saved"
      stEvt:instanceID="xmp.iid:8899d0c8-67c7-c44d-824f-4a3c1fa1d206"
      stEvt:when="2025-05-20T19:28:21+08:00"
      stEvt:softwareAgent="Adobe Adobe Media Encoder 2024.0 (Windows)"
      stEvt:changed="/"/>
     <rdf:li
      stEvt:action="saved"
      stEvt:instanceID="xmp.iid:11d7535f-5f1a-0c4f-b9d9-1a85956e9278"
      stEvt:when="2025-05-20T19:28:21+08:00"
      stEvt:softwareAgent="Adobe Adobe Media Encoder 2024.0 (Windows)"
      stEvt:changed="/metadata"/>
    </rdf:Seq>
   </xmpMM:History>
   <xmpMM:DerivedFrom
    stRef:instanceID="e038e777-3228-8ab2-9028-219b00000084"
    stRef:documentID="b34f583b-da9b-0794-7c45-8a2600000057"
    stRef:originalDocumentID="xmp.did:19371b53-3cf7-174e-bf2d-2a0184d45f58"/>
   <xmpDM:startTimecode
    xmpDM:timeFormat="AudioSamplesTimecode"
    xmpDM:timeValue="00:00:00:00000"/>
   <xmpDM:duration
    xmpDM:value="11865443"
    xmpDM:scale="1/48000"/>
   <xmpDM:altTimecode
    xmpDM:timeValue="00:00:00:00000"
    xmpDM:timeFormat="AudioSamplesTimecode"/>
   <riffinfo:name>
    <rdf:Alt>
     <rdf:li xml:lang="x-default">Infraction</rdf:li>
    </rdf:Alt>
   </riffinfo:name>
   <dc:title>
    <rdf:Alt>
     <rdf:li xml:lang="x-default">Infraction</rdf:li>
    </rdf:Alt>
   </dc:title>
  </rdf:Description>
 </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?>