using UnityEngine;
using UnityEditor;

/// <summary>
/// 编辑器工具：平衡车辆加速度，解决加速过快问题
/// </summary>
public class VehicleAccelerationBalancer : EditorWindow
{
    [MenuItem("Tools/Vehicle/Balance Vehicle Acceleration")]
    public static void ShowWindow()
    {
        GetWindow<VehicleAccelerationBalancer>("Vehicle Acceleration Balancer");
    }

    private float torqueReductionFactor = 0.5f;
    private float powerFalloffStart = 0.75f;
    private float targetSpeedReduction = 0.85f;

    private void OnGUI()
    {
        GUILayout.Label("车辆加速度平衡工具", EditorStyles.boldLabel);
        GUILayout.Space(10);

        GUILayout.Label("当前问题：车辆在1-2秒内达到最大速度", EditorStyles.wordWrappedLabel);
        GUILayout.Label("解决方案：调整扭矩、功率衰减和扭矩曲线参数", EditorStyles.wordWrappedLabel);
        
        GUILayout.Space(15);
        GUILayout.Label("调整参数", EditorStyles.boldLabel);
        
        torqueReductionFactor = EditorGUILayout.Slider("扭矩减少因子", torqueReductionFactor, 0.3f, 0.8f);
        GUILayout.Label($"扭矩将减少到原来的 {torqueReductionFactor * 100:F0}%", EditorStyles.miniLabel);
        
        powerFalloffStart = EditorGUILayout.Slider("功率衰减起始点", powerFalloffStart, 0.6f, 0.9f);
        GUILayout.Label($"在 {powerFalloffStart * 100:F0}% 目标速度时开始功率衰减", EditorStyles.miniLabel);
        
        targetSpeedReduction = EditorGUILayout.Slider("目标速度调整因子", targetSpeedReduction, 0.7f, 0.95f);
        GUILayout.Label($"目标引擎速度调整到原来的 {targetSpeedReduction * 100:F0}%", EditorStyles.miniLabel);
        
        GUILayout.Space(20);

        if (GUILayout.Button("应用平衡调整", GUILayout.Height(30)))
        {
            ApplyAccelerationBalance();
        }

        GUILayout.Space(10);

        if (GUILayout.Button("恢复原始参数", GUILayout.Height(25)))
        {
            RestoreOriginalParameters();
        }

        GUILayout.Space(15);
        GUILayout.Label("推荐设置", EditorStyles.boldLabel);
        if (GUILayout.Button("应用推荐的平衡设置"))
        {
            torqueReductionFactor = 0.55f;
            powerFalloffStart = 0.75f;
            targetSpeedReduction = 0.85f;
            ApplyAccelerationBalance();
        }
        
        GUILayout.Space(10);
        GUILayout.Label("说明：", EditorStyles.boldLabel);
        GUILayout.Label("• 扭矩减少：降低基础加速力", EditorStyles.miniLabel);
        GUILayout.Label("• 功率衰减：提前开始功率下降", EditorStyles.miniLabel);
        GUILayout.Label("• 目标速度：调整最佳性能速度范围", EditorStyles.miniLabel);
        GUILayout.Label("• 扭矩曲线：优化为更真实的曲线", EditorStyles.miniLabel);
    }

    private void ApplyAccelerationBalance()
    {
        string[] vehicleGuids = AssetDatabase.FindAssets("t:VehicleData");
        int balancedCount = 0;

        foreach (string guid in vehicleGuids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            VehicleData vehicleData = AssetDatabase.LoadAssetAtPath<VehicleData>(path);

            if (vehicleData != null)
            {
                // 记录原始值
                float originalTorque = vehicleData.m_BaseMaxMotorTorque;
                float originalFalloffStart = vehicleData.m_BaseEnginePowerFalloffStartFactor;
                float originalTargetSpeed = vehicleData.m_TargetEngineSpeedKPH;
                
                // 1. 降低基础扭矩
                vehicleData.m_BaseMaxMotorTorque *= torqueReductionFactor;
                
                // 2. 提前功率衰减起始点
                vehicleData.m_BaseEnginePowerFalloffStartFactor = powerFalloffStart;
                
                // 3. 调整目标引擎速度
                vehicleData.m_TargetEngineSpeedKPH *= targetSpeedReduction;
                
                // 4. 优化扭矩曲线 - 更真实的扭矩分布
                AnimationCurve realisticTorqueCurve = new AnimationCurve();
                realisticTorqueCurve.AddKey(0f, 1.0f);      // 起步：正常扭矩
                realisticTorqueCurve.AddKey(0.25f, 1.15f);  // 25%速度：峰值扭矩
                realisticTorqueCurve.AddKey(0.5f, 1.0f);    // 50%速度：正常扭矩
                realisticTorqueCurve.AddKey(0.75f, 0.85f);  // 75%速度：扭矩下降
                realisticTorqueCurve.AddKey(1.0f, 0.7f);    // 最高速度：低扭矩
                
                // 设置曲线的切线为平滑
                for (int i = 0; i < realisticTorqueCurve.length; i++)
                {
                    AnimationUtility.SetKeyLeftTangentMode(realisticTorqueCurve, i, AnimationUtility.TangentMode.Auto);
                    AnimationUtility.SetKeyRightTangentMode(realisticTorqueCurve, i, AnimationUtility.TangentMode.Auto);
                }
                
                vehicleData.m_DefaultEngineTorqueCurve = realisticTorqueCurve;
                
                // 5. 确保功率在绝对最大速度时的合理值
                vehicleData.m_BaseEnginePowerAtAbsoluteMaxFactor = Mathf.Clamp(
                    vehicleData.m_BaseEnginePowerAtAbsoluteMaxFactor, 0.05f, 0.15f);
                
                // 标记为已修改
                EditorUtility.SetDirty(vehicleData);
                balancedCount++;
                
                Debug.Log($"平衡车辆 '{vehicleData.m_VehicleName}':\n" +
                         $"  扭矩: {originalTorque:F0} → {vehicleData.m_BaseMaxMotorTorque:F0}\n" +
                         $"  功率衰减起始: {originalFalloffStart:F2} → {vehicleData.m_BaseEnginePowerFalloffStartFactor:F2}\n" +
                         $"  目标速度: {originalTargetSpeed:F0} → {vehicleData.m_TargetEngineSpeedKPH:F0}");
            }
        }

        // 保存所有修改
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        EditorUtility.DisplayDialog("平衡完成", 
            $"成功平衡了 {balancedCount} 个车辆的加速参数。\n\n" +
            "主要改进：\n" +
            $"• 扭矩减少到原来的 {torqueReductionFactor * 100:F0}%\n" +
            $"• 功率衰减提前到 {powerFalloffStart * 100:F0}% 速度\n" +
            $"• 目标速度调整到原来的 {targetSpeedReduction * 100:F0}%\n" +
            "• 优化了扭矩曲线为更真实的分布\n\n" +
            "现在车辆加速应该更加平衡和真实！", "确定");
    }

    private void RestoreOriginalParameters()
    {
        if (!EditorUtility.DisplayDialog("确认恢复", 
            "这将恢复所有车辆的原始加速参数。\n\n" +
            "注意：这会让车辆重新变得加速过快！\n\n确定要继续吗？", "确定", "取消"))
        {
            return;
        }

        string[] vehicleGuids = AssetDatabase.FindAssets("t:VehicleData");
        int restoredCount = 0;

        foreach (string guid in vehicleGuids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            VehicleData vehicleData = AssetDatabase.LoadAssetAtPath<VehicleData>(path);

            if (vehicleData != null)
            {
                // 根据车辆类型恢复不同的原始参数
                if (vehicleData.m_VehicleName.Contains("sedan") || vehicleData.m_VehicleName.Contains("轿车"))
                {
                    vehicleData.m_BaseMaxMotorTorque = 5000f;
                    vehicleData.m_BaseEnginePowerFalloffStartFactor = 0.95f;
                    vehicleData.m_TargetEngineSpeedKPH = 90f;
                }
                else if (vehicleData.m_VehicleName.Contains("Police") || vehicleData.m_VehicleName.Contains("警车"))
                {
                    vehicleData.m_BaseMaxMotorTorque = 4000f;
                    vehicleData.m_BaseEnginePowerFalloffStartFactor = 0.85f;
                    vehicleData.m_TargetEngineSpeedKPH = 80f;
                }
                else
                {
                    // 其他车辆的默认值
                    vehicleData.m_BaseMaxMotorTorque = 3500f;
                    vehicleData.m_BaseEnginePowerFalloffStartFactor = 0.9f;
                    vehicleData.m_TargetEngineSpeedKPH = 85f;
                }
                
                vehicleData.m_BaseEnginePowerAtAbsoluteMaxFactor = 0.15f;
                
                // 恢复原始扭矩曲线
                vehicleData.m_DefaultEngineTorqueCurve = AnimationCurve.Linear(0, 0.8f, 1, 0.6f);
                
                // 标记为已修改
                EditorUtility.SetDirty(vehicleData);
                restoredCount++;
            }
        }

        // 保存所有修改
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        EditorUtility.DisplayDialog("恢复完成", 
            $"成功恢复了 {restoredCount} 个车辆的原始加速参数。", "确定");
    }
}
