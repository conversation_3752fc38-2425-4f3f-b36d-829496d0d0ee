<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 9.0-c001 152.deb9585, 2024/02/06-08:36:10        " rdfhash="1D17C7C46CE124291FCAA451F903038C" merged="0">
 <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
  <rdf:Description rdf:about=""
    xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
    xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#"
    xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"
    xmlns:xmp="http://ns.adobe.com/xap/1.0/"
    xmlns:xmpDM="http://ns.adobe.com/xmp/1.0/DynamicMedia/"
    xmlns:riffinfo="http://ns.adobe.com/riff/info/"
    xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmpMM:InstanceID="xmp.iid:c5e8433b-6699-5549-ab59-eaaf6a1c70d2"
   xmpMM:DocumentID="80c0fdf3-ca15-a8db-cff2-9fa700000057"
   xmpMM:OriginalDocumentID="xmp.did:b29e3921-09f0-0c45-8862-5a203ea249e8"
   xmp:MetadataDate="2025-05-20T19:27:23+08:00"
   xmp:ModifyDate="2025-05-20T19:27:23+08:00"
   xmp:CreatorTool="Lavf61.7.100"
   xmp:CreateDate="2025-05-20T19:26:51+08:00"
   xmpDM:artist="Kirara Magic"
   xmpDM:audioSampleRate="48000"
   xmpDM:audioSampleType="16Int"
   xmpDM:audioChannelType="Stereo"
   xmpDM:startTimeScale="30000"
   xmpDM:startTimeSampleSize="1001"
   riffinfo:product="Index"
   dc:format="AAC 音频">
   <xmpMM:History>
    <rdf:Seq>
     <rdf:li
      stEvt:action="saved"
      stEvt:instanceID="8764c2e3-8d68-f857-517c-32d900000084"
      stEvt:when="2025-05-20T19:27:23+08:00"
      stEvt:softwareAgent="Adobe Adobe Media Encoder 2024.0 (Windows)"
      stEvt:changed="/"/>
     <rdf:li
      stEvt:action="saved"
      stEvt:instanceID="1a96dd2e-e970-c4ae-9f97-dae000000084"
      stEvt:when="2025-05-20T19:19:44+08:00"
      stEvt:softwareAgent="Adobe Photoshop 24.4 (20230317.orig.1470 c2e7adb)  (Windows)"
      stEvt:changed="/"/>
     <rdf:li
      stEvt:action="saved"
      stEvt:instanceID="xmp.iid:b5cf5f66-4f19-ac42-b515-518557057a44"
      stEvt:when="2025-05-20T19:27:23+08:00"
      stEvt:softwareAgent="Adobe Adobe Media Encoder 2024.0 (Windows)"
      stEvt:changed="/"/>
     <rdf:li
      stEvt:action="saved"
      stEvt:instanceID="xmp.iid:c5e8433b-6699-5549-ab59-eaaf6a1c70d2"
      stEvt:when="2025-05-20T19:27:23+08:00"
      stEvt:softwareAgent="Adobe Adobe Media Encoder 2024.0 (Windows)"
      stEvt:changed="/metadata"/>
    </rdf:Seq>
   </xmpMM:History>
   <xmpMM:DerivedFrom
    stRef:instanceID="1a96dd2e-e970-c4ae-9f97-dae000000084"
    stRef:documentID="ea86f59e-0c63-d1eb-50f0-a6d600000057"
    stRef:originalDocumentID="xmp.did:d6e1e749-8d56-3a48-a00a-9123d2b67370"/>
   <xmpDM:startTimecode
    xmpDM:timeFormat="AudioSamplesTimecode"
    xmpDM:timeValue="00:00:00:00000"/>
   <xmpDM:duration
    xmpDM:value="7890937"
    xmpDM:scale="1/48000"/>
   <xmpDM:altTimecode
    xmpDM:timeValue="00:00:00:00000"
    xmpDM:timeFormat="AudioSamplesTimecode"/>
   <riffinfo:name>
    <rdf:Alt>
     <rdf:li xml:lang="x-default">Index</rdf:li>
    </rdf:Alt>
   </riffinfo:name>
   <dc:title>
    <rdf:Alt>
     <rdf:li xml:lang="x-default">Index</rdf:li>
    </rdf:Alt>
   </dc:title>
  </rdf:Description>
 </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?>