using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// 氮气视觉效果管理器
/// 负责管理氮气加速时的各种视觉特效，包括火焰、光效、冲击波等
/// </summary>
public class NitroVisualEffectsManager : MonoBehaviour
{
    [Header("氮气火焰效果")]
    [Tooltip("氮气火焰粒子系统预制体")]
    [SerializeField] private GameObject nitroFlamePrefab;
    
    [Tooltip("氮气火焰生成位置（通常是排气管位置）")]
    [SerializeField] private Transform[] nitroFlamePositions;
    
    [Tooltip("火焰效果强度倍数")]
    [SerializeField] private float flameIntensityMultiplier = 1.0f;

    [Header("氮气光效")]
    [Tooltip("氮气激活时的光源")]
    [SerializeField] private Light[] nitroLights;
    
    [Tooltip("氮气光效颜色")]
    [SerializeField] private Color nitroLightColor = Color.cyan;
    
    [Tooltip("氮气光效最大强度")]
    [SerializeField] private float maxLightIntensity = 2.0f;
    
    [Toolt<PERSON>("光效闪烁频率")]
    [SerializeField] private float lightFlickerFrequency = 10f;

    [Header("地面冲击波效果")]
    [Tooltip("地面冲击波粒子系统预制体")]
    [SerializeField] private GameObject groundShockwavePrefab;
    
    [Tooltip("冲击波生成位置（车辆底部）")]
    [SerializeField] private Transform shockwavePosition;
    
    [Tooltip("冲击波触发的最小速度")]
    [SerializeField] private float minSpeedForShockwave = 30f;

    [Header("车身发光效果")]
    [Tooltip("车身发光材质")]
    [SerializeField] private Material[] glowMaterials;
    
    [Tooltip("发光强度")]
    [SerializeField] private float glowIntensity = 2.0f;
    
    [Tooltip("发光颜色")]
    [SerializeField] private Color glowColor = Color.blue;

    [Header("速度线条效果")]
    [Tooltip("速度线条粒子系统预制体")]
    [SerializeField] private GameObject speedLinesPrefab;
    
    [Tooltip("速度线条生成位置")]
    [SerializeField] private Transform speedLinesPosition;

    [Header("引用")]
    [Tooltip("车辆控制器")]
    [SerializeField] private CarController carController;

    // 私有变量
    private List<ParticleSystem> activeFlameEffects = new List<ParticleSystem>();
    private List<ParticleSystem> activeSpeedLines = new List<ParticleSystem>();
    private ParticleSystem currentShockwave;
    private bool wasNitroActive = false;
    private float lightFlickerTimer = 0f;
    private Dictionary<Material, Color> originalEmissionColors = new Dictionary<Material, Color>();

    private void Awake()
    {
        // 自动获取车辆控制器
        if (carController == null)
        {
            carController = GetComponent<CarController>();
            if (carController == null)
            {
                carController = GetComponentInParent<CarController>();
            }
        }

        // 初始化光源
        InitializeLights();
        
        // 保存原始发光材质颜色
        InitializeGlowMaterials();
    }

    private void InitializeLights()
    {
        if (nitroLights != null)
        {
            foreach (Light light in nitroLights)
            {
                if (light != null)
                {
                    light.color = nitroLightColor;
                    light.intensity = 0f;
                    light.enabled = false;
                }
            }
        }
    }

    private void InitializeGlowMaterials()
    {
        if (glowMaterials != null)
        {
            foreach (Material material in glowMaterials)
            {
                if (material != null && material.HasProperty("_EmissionColor"))
                {
                    originalEmissionColors[material] = material.GetColor("_EmissionColor");
                }
            }
        }
    }

    private void Update()
    {
        if (carController == null) return;

        bool isNitroActive = carController.IsNitroActiveAndEnabled;
        float currentSpeed = carController.GetCurrentForwardSpeedMS() * 3.6f; // 转换为km/h

        // 更新各种效果
        UpdateFlameEffects(isNitroActive);
        UpdateLightEffects(isNitroActive);
        UpdateShockwaveEffects(isNitroActive, currentSpeed);
        UpdateGlowEffects(isNitroActive);
        UpdateSpeedLines(isNitroActive, currentSpeed);

        wasNitroActive = isNitroActive;
    }

    /// <summary>
    /// 更新火焰效果
    /// </summary>
    private void UpdateFlameEffects(bool isNitroActive)
    {
        if (nitroFlamePrefab == null || nitroFlamePositions == null) return;

        if (isNitroActive && !wasNitroActive)
        {
            // 氮气刚激活，创建火焰效果
            CreateFlameEffects();
        }
        else if (!isNitroActive && wasNitroActive)
        {
            // 氮气刚关闭，停止火焰效果
            StopFlameEffects();
        }

        // 更新火焰强度
        if (isNitroActive)
        {
            UpdateFlameIntensity();
        }
    }

    private void CreateFlameEffects()
    {
        foreach (Transform position in nitroFlamePositions)
        {
            if (position != null)
            {
                GameObject flameObject = Instantiate(nitroFlamePrefab, position.position, position.rotation, position);
                ParticleSystem flameParticles = flameObject.GetComponent<ParticleSystem>();
                
                if (flameParticles != null)
                {
                    activeFlameEffects.Add(flameParticles);
                    flameParticles.Play();
                }
            }
        }
    }

    private void StopFlameEffects()
    {
        foreach (ParticleSystem flame in activeFlameEffects)
        {
            if (flame != null)
            {
                flame.Stop();
                Destroy(flame.gameObject, 2f); // 延迟销毁，让粒子自然消失
            }
        }
        activeFlameEffects.Clear();
    }

    private void UpdateFlameIntensity()
    {
        float speedFactor = Mathf.Clamp01(carController.GetCurrentForwardSpeedMS() / 30f);
        
        foreach (ParticleSystem flame in activeFlameEffects)
        {
            if (flame != null)
            {
                var emission = flame.emission;
                emission.rateOverTime = Mathf.Lerp(50f, 150f, speedFactor) * flameIntensityMultiplier;
            }
        }
    }

    /// <summary>
    /// 更新光效
    /// </summary>
    private void UpdateLightEffects(bool isNitroActive)
    {
        if (nitroLights == null) return;

        lightFlickerTimer += Time.deltaTime * lightFlickerFrequency;

        foreach (Light light in nitroLights)
        {
            if (light != null)
            {
                if (isNitroActive)
                {
                    light.enabled = true;
                    
                    // 添加闪烁效果
                    float flicker = 1f + Mathf.Sin(lightFlickerTimer) * 0.1f;
                    light.intensity = maxLightIntensity * flicker;
                }
                else
                {
                    light.enabled = false;
                    light.intensity = 0f;
                }
            }
        }
    }

    /// <summary>
    /// 更新冲击波效果
    /// </summary>
    private void UpdateShockwaveEffects(bool isNitroActive, float currentSpeed)
    {
        if (groundShockwavePrefab == null || shockwavePosition == null) return;

        if (isNitroActive && !wasNitroActive && currentSpeed > minSpeedForShockwave)
        {
            // 创建地面冲击波
            CreateShockwave();
        }
    }

    private void CreateShockwave()
    {
        if (currentShockwave != null)
        {
            Destroy(currentShockwave.gameObject);
        }

        GameObject shockwaveObject = Instantiate(groundShockwavePrefab, shockwavePosition.position, shockwavePosition.rotation);
        currentShockwave = shockwaveObject.GetComponent<ParticleSystem>();
        
        if (currentShockwave != null)
        {
            currentShockwave.Play();
            Destroy(shockwaveObject, 3f); // 3秒后销毁
        }
    }

    /// <summary>
    /// 更新发光效果
    /// </summary>
    private void UpdateGlowEffects(bool isNitroActive)
    {
        if (glowMaterials == null) return;

        foreach (Material material in glowMaterials)
        {
            if (material != null && material.HasProperty("_EmissionColor"))
            {
                if (isNitroActive)
                {
                    Color targetGlow = glowColor * glowIntensity;
                    material.SetColor("_EmissionColor", targetGlow);
                    material.EnableKeyword("_EMISSION");
                }
                else
                {
                    if (originalEmissionColors.ContainsKey(material))
                    {
                        material.SetColor("_EmissionColor", originalEmissionColors[material]);
                    }
                    else
                    {
                        material.SetColor("_EmissionColor", Color.black);
                        material.DisableKeyword("_EMISSION");
                    }
                }
            }
        }
    }

    /// <summary>
    /// 更新速度线条效果
    /// </summary>
    private void UpdateSpeedLines(bool isNitroActive, float currentSpeed)
    {
        if (speedLinesPrefab == null || speedLinesPosition == null) return;

        if (isNitroActive && currentSpeed > 50f && activeSpeedLines.Count == 0)
        {
            // 创建速度线条
            CreateSpeedLines();
        }
        else if ((!isNitroActive || currentSpeed <= 30f) && activeSpeedLines.Count > 0)
        {
            // 停止速度线条
            StopSpeedLines();
        }
    }

    private void CreateSpeedLines()
    {
        GameObject speedLinesObject = Instantiate(speedLinesPrefab, speedLinesPosition.position, speedLinesPosition.rotation, speedLinesPosition);
        ParticleSystem speedLines = speedLinesObject.GetComponent<ParticleSystem>();
        
        if (speedLines != null)
        {
            activeSpeedLines.Add(speedLines);
            speedLines.Play();
        }
    }

    private void StopSpeedLines()
    {
        foreach (ParticleSystem speedLines in activeSpeedLines)
        {
            if (speedLines != null)
            {
                speedLines.Stop();
                Destroy(speedLines.gameObject, 1f);
            }
        }
        activeSpeedLines.Clear();
    }

    private void OnDestroy()
    {
        // 清理所有效果
        StopFlameEffects();
        StopSpeedLines();
        
        if (currentShockwave != null)
        {
            Destroy(currentShockwave.gameObject);
        }
    }
}
