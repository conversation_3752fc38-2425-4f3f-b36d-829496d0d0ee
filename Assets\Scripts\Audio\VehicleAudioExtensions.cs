using UnityEngine;

/// <summary>
/// 车辆音频扩展类
/// 提供用于处理车辆音频的扩展方法
/// </summary>
public static class VehicleAudioExtensions
{
    /// <summary>
    /// 初始化车辆音频系统
    /// </summary>
    /// <param name="vehicle">车辆控制器</param>
    /// <param name="engineClip">引擎音效 (来自VehicleData或PartData)</param>
    /// <param name="driftClip">漂移音效 (来自VehicleData或PartData)</param>
    /// <param name="nitroClip">氮气音效 (来自VehicleData或PartData)</param>
    /// <param name="engineVolume">引擎音量</param>
    /// <param name="driftVolume">漂移音量</param>
    /// <param name="nitroVolume">氮气音量</param>
    /// <param name="spatialBlend">2D/3D混合 (0=2D, 1=3D)</param>
    public static void InitializeVehicleAudio(this CarController vehicle, AudioClip engineClip = null, AudioClip driftClip = null, AudioClip nitroClip = null,
                                             float engineVolume = 0.7f, float driftVolume = 0.6f, float nitroVolume = 0.8f, float spatialBlend = 1.0f)
    {
        if (AudioManager.Instance == null)
        {
            Debug.LogWarning("VehicleAudioExtensions: AudioManager实例未找到，无法初始化车辆音频");
            return;
        }

        string vehicleId = vehicle.gameObject.GetInstanceID().ToString();

        // 初始化引擎音频
        AudioSource engineSource = AudioManager.Instance.GetVehicleAudioSource(
            vehicleId, "Engine", engineClip, engineVolume, true, spatialBlend, true);

        // 初始化漂移音频
        AudioSource driftSource = AudioManager.Instance.GetVehicleAudioSource(
            vehicleId, "Drift", driftClip, driftVolume, true, spatialBlend, false);

        // 初始化氮气音频
        AudioSource nitroSource = AudioManager.Instance.GetVehicleAudioSource(
            vehicleId, "Nitro", nitroClip, nitroVolume, true, spatialBlend, false);

        // 将音频源设置为车辆的子对象
        if (engineSource != null)
        {
            engineSource.transform.SetParent(vehicle.transform);
            engineSource.transform.localPosition = Vector3.zero;
        }

        if (driftSource != null)
        {
            driftSource.transform.SetParent(vehicle.transform);
            driftSource.transform.localPosition = Vector3.zero;
        }

        if (nitroSource != null)
        {
            nitroSource.transform.SetParent(vehicle.transform);
            nitroSource.transform.localPosition = Vector3.zero;
        }
    }

    /// <summary>
    /// 更新车辆音频
    /// </summary>
    /// <param name="vehicle">车辆控制器</param>
    /// <param name="normalizedSpeed">归一化速度 (0-1)</param>
    /// <param name="motorInput">马达输入 (0-1)</param>
    /// <param name="isDrifting">是否正在漂移</param>
    /// <param name="isNitroActive">是否正在使用氮气</param>
    /// <param name="pitchCurve">音调曲线</param>
    /// <param name="minPitch">最小音调</param>
    /// <param name="maxPitch">最大音调</param>
    /// <param name="inputPitchFactor">输入音调因子</param>
    public static void UpdateVehicleAudio(this CarController vehicle, float normalizedSpeed, float motorInput, bool isDrifting, bool isNitroActive,
                                         AnimationCurve pitchCurve, float minPitch = 0.5f, float maxPitch = 2.0f, float inputPitchFactor = 0.2f)
    {
        if (AudioManager.Instance == null) return;

        string vehicleId = vehicle.gameObject.GetInstanceID().ToString();

        // 更新引擎音频
        AudioManager.Instance.UpdateVehicleEngineAudio(vehicleId, normalizedSpeed, motorInput, pitchCurve, minPitch, maxPitch, inputPitchFactor);

        // 更新漂移音频
        AudioManager.Instance.UpdateVehicleDriftAudio(vehicleId, isDrifting);

        // 更新氮气音频
        AudioManager.Instance.UpdateVehicleNitroAudio(vehicleId, isNitroActive);
    }
}
