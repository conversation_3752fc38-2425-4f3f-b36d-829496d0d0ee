using UnityEngine;
using UnityEditor;

/// <summary>
/// 编辑器工具：批量优化车辆音效参数
/// </summary>
public class VehicleAudioOptimizer : EditorWindow
{
    [MenuItem("Tools/Vehicle/Optimize Vehicle Audio System")]
    public static void ShowWindow()
    {
        GetWindow<VehicleAudioOptimizer>("Vehicle Audio Optimizer");
    }

    private void OnGUI()
    {
        GUILayout.Label("车辆音效系统优化工具", EditorStyles.boldLabel);
        GUILayout.Space(10);

        GUILayout.Label("此工具将优化所有车辆数据的音效参数：", EditorStyles.wordWrappedLabel);
        GUILayout.Label("• 添加独立的引擎音高曲线，解决高速时音高下降问题", EditorStyles.miniLabel);
        GUILayout.Label("• 优化引擎音高范围，提供更有力的引擎声", EditorStyles.miniLabel);
        GUILayout.Label("• 为氮气系统添加音效支持", EditorStyles.miniLabel);
        
        GUILayout.Space(20);

        if (GUILayout.Button("优化所有车辆音效参数", GUILayout.Height(30)))
        {
            OptimizeAllVehiclesAudio();
        }

        GUILayout.Space(10);

        if (GUILayout.Button("恢复默认音效参数", GUILayout.Height(25)))
        {
            RestoreDefaultAudio();
        }

        GUILayout.Space(20);
        GUILayout.Label("音效测试工具", EditorStyles.boldLabel);
        
        if (GUILayout.Button("测试引擎音高曲线"))
        {
            TestEnginePitchCurve();
        }
    }

    private void OptimizeAllVehiclesAudio()
    {
        string[] vehicleGuids = AssetDatabase.FindAssets("t:VehicleData");
        int optimizedCount = 0;

        foreach (string guid in vehicleGuids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            VehicleData vehicleData = AssetDatabase.LoadAssetAtPath<VehicleData>(path);

            if (vehicleData != null)
            {
                // 优化引擎音高参数
                vehicleData.m_EngineMinPitch = Mathf.Max(0.6f, vehicleData.m_EngineMinPitch);
                vehicleData.m_EngineMaxPitch = Mathf.Max(2.5f, vehicleData.m_EngineMaxPitch);
                vehicleData.m_EngineInputPitchFactor = Mathf.Max(0.4f, vehicleData.m_EngineInputPitchFactor);
                
                // 创建优化的引擎音高曲线 - 高速时音高上升而不是下降
                AnimationCurve optimizedPitchCurve = new AnimationCurve();
                optimizedPitchCurve.AddKey(0f, 0.8f);      // 怠速：低音高
                optimizedPitchCurve.AddKey(0.3f, 1.0f);    // 30%速度：正常音高
                optimizedPitchCurve.AddKey(0.7f, 1.6f);    // 70%速度：较高音高
                optimizedPitchCurve.AddKey(1.0f, 2.2f);    // 最高速度：最高音高
                
                // 设置曲线的切线为平滑
                for (int i = 0; i < optimizedPitchCurve.length; i++)
                {
                    AnimationUtility.SetKeyLeftTangentMode(optimizedPitchCurve, i, AnimationUtility.TangentMode.Auto);
                    AnimationUtility.SetKeyRightTangentMode(optimizedPitchCurve, i, AnimationUtility.TangentMode.Auto);
                }
                
                vehicleData.m_EngineAudioPitchCurve = optimizedPitchCurve;
                
                // 标记为已修改
                EditorUtility.SetDirty(vehicleData);
                optimizedCount++;
                
                Debug.Log($"优化车辆音效 '{vehicleData.m_VehicleName}': 引擎音高范围 {vehicleData.m_EngineMinPitch}-{vehicleData.m_EngineMaxPitch}");
            }
        }

        // 保存所有修改
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        EditorUtility.DisplayDialog("优化完成", 
            $"成功优化了 {optimizedCount} 个车辆的音效参数。\n\n" +
            "所有车辆现在都具有：\n" +
            "• 独立的引擎音高曲线\n" +
            "• 优化的音高范围\n" +
            "• 氮气音效支持", "确定");
    }

    private void RestoreDefaultAudio()
    {
        if (!EditorUtility.DisplayDialog("确认恢复", 
            "这将恢复所有车辆的默认音效参数。此操作无法撤销。\n\n确定要继续吗？", "确定", "取消"))
        {
            return;
        }

        string[] vehicleGuids = AssetDatabase.FindAssets("t:VehicleData");
        int restoredCount = 0;

        foreach (string guid in vehicleGuids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            VehicleData vehicleData = AssetDatabase.LoadAssetAtPath<VehicleData>(path);

            if (vehicleData != null)
            {
                // 恢复默认音效参数
                vehicleData.m_EngineMinPitch = 0.5f;
                vehicleData.m_EngineMaxPitch = 2.0f;
                vehicleData.m_EngineInputPitchFactor = 0.3f;
                vehicleData.m_EngineAudioPitchCurve = AnimationCurve.Linear(0, 0.8f, 1, 2.2f);
                
                // 标记为已修改
                EditorUtility.SetDirty(vehicleData);
                restoredCount++;
            }
        }

        // 保存所有修改
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        EditorUtility.DisplayDialog("恢复完成", 
            $"成功恢复了 {restoredCount} 个车辆的默认音效参数。", "确定");
    }

    private void TestEnginePitchCurve()
    {
        // 在Scene视图中显示引擎音高曲线的可视化
        Debug.Log("引擎音高曲线测试：");
        Debug.Log("速度 0% -> 音高 0.8");
        Debug.Log("速度 30% -> 音高 1.0");
        Debug.Log("速度 70% -> 音高 1.6");
        Debug.Log("速度 100% -> 音高 2.2");
        Debug.Log("这样可以确保高速时引擎声音更有力！");
    }
}
