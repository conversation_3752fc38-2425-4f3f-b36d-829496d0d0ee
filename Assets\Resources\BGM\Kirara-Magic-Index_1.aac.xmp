<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 9.0-c001 152.deb9585, 2024/02/06-08:36:10        " rdfhash="1C7B0F2A44D2E6CE3C6B437A826903A7" merged="0">
 <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
  <rdf:Description rdf:about=""
    xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
    xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#"
    xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"
    xmlns:xmp="http://ns.adobe.com/xap/1.0/"
    xmlns:xmpDM="http://ns.adobe.com/xmp/1.0/DynamicMedia/"
    xmlns:riffinfo="http://ns.adobe.com/riff/info/"
    xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmpMM:InstanceID="xmp.iid:f8f84253-a197-5945-ba3a-0e87f15f7166"
   xmpMM:DocumentID="4e2b9c7b-8e2d-7a10-6975-2f760000005b"
   xmpMM:OriginalDocumentID="xmp.did:750a57c4-25a0-bb49-8f09-0ae4c2886025"
   xmp:MetadataDate="2025-05-20T19:28:17+08:00"
   xmp:ModifyDate="2025-05-20T19:28:17+08:00"
   xmp:CreatorTool="Lavf61.7.100"
   xmp:CreateDate="2025-05-20T19:27:46+08:00"
   xmpDM:artist="Kirara Magic"
   xmpDM:audioSampleRate="48000"
   xmpDM:audioSampleType="16Int"
   xmpDM:audioChannelType="Stereo"
   xmpDM:startTimeScale="30000"
   xmpDM:startTimeSampleSize="1001"
   riffinfo:product="Index"
   dc:format="AAC 音频">
   <xmpMM:History>
    <rdf:Seq>
     <rdf:li
      stEvt:action="saved"
      stEvt:instanceID="9c4e3184-0cdf-5f8b-092c-c94200000088"
      stEvt:when="2025-05-20T19:28:17+08:00"
      stEvt:softwareAgent="Adobe Adobe Media Encoder 2024.0 (Windows)"
      stEvt:changed="/"/>
     <rdf:li
      stEvt:action="saved"
      stEvt:instanceID="1a96dd2e-e970-c4ae-9f97-dae000000084"
      stEvt:when="2025-05-20T19:19:44+08:00"
      stEvt:softwareAgent="Adobe Photoshop 24.4 (20230317.orig.1470 c2e7adb)  (Windows)"
      stEvt:changed="/"/>
     <rdf:li
      stEvt:action="saved"
      stEvt:instanceID="xmp.iid:e2427d4b-6dce-f245-993f-f279b0248c13"
      stEvt:when="2025-05-20T19:28:17+08:00"
      stEvt:softwareAgent="Adobe Adobe Media Encoder 2024.0 (Windows)"
      stEvt:changed="/"/>
     <rdf:li
      stEvt:action="saved"
      stEvt:instanceID="xmp.iid:f8f84253-a197-5945-ba3a-0e87f15f7166"
      stEvt:when="2025-05-20T19:28:17+08:00"
      stEvt:softwareAgent="Adobe Adobe Media Encoder 2024.0 (Windows)"
      stEvt:changed="/metadata"/>
    </rdf:Seq>
   </xmpMM:History>
   <xmpMM:DerivedFrom
    stRef:instanceID="1a96dd2e-e970-c4ae-9f97-dae000000084"
    stRef:documentID="ea86f59e-0c63-d1eb-50f0-a6d600000057"
    stRef:originalDocumentID="xmp.did:d6e1e749-8d56-3a48-a00a-9123d2b67370"/>
   <xmpDM:startTimecode
    xmpDM:timeFormat="AudioSamplesTimecode"
    xmpDM:timeValue="00:00:00:00000"/>
   <xmpDM:duration
    xmpDM:value="7890937"
    xmpDM:scale="1/48000"/>
   <xmpDM:altTimecode
    xmpDM:timeValue="00:00:00:00000"
    xmpDM:timeFormat="AudioSamplesTimecode"/>
   <riffinfo:name>
    <rdf:Alt>
     <rdf:li xml:lang="x-default">Index</rdf:li>
    </rdf:Alt>
   </riffinfo:name>
   <dc:title>
    <rdf:Alt>
     <rdf:li xml:lang="x-default">Index</rdf:li>
    </rdf:Alt>
   </dc:title>
  </rdf:Description>
 </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?>