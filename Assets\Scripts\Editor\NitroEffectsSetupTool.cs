using UnityEngine;
using UnityEditor;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

/// <summary>
/// 氮气效果设置工具
/// 帮助快速设置氮气加速的视觉效果
/// </summary>
public class NitroEffectsSetupTool : EditorWindow
{
    private GameObject selectedVehicle;
    private Camera selectedCamera;
    private bool createPostProcessVolume = true;
    private bool setupNitroVisualEffects = true;
    private bool setupSpeedLinesEffect = true;
    private bool setupCameraEnhancements = true;

    [MenuItem("Tools/Vehicle/Nitro Effects Setup")]
    public static void ShowWindow()
    {
        GetWindow<NitroEffectsSetupTool>("氮气效果设置工具");
    }

    private void OnGUI()
    {
        GUILayout.Label("氮气视觉效果设置工具", EditorStyles.boldLabel);
        GUILayout.Space(10);

        // 选择车辆
        GUILayout.Label("1. 选择车辆和相机", EditorStyles.boldLabel);
        selectedVehicle = (GameObject)EditorGUILayout.ObjectField("车辆对象", selectedVehicle, typeof(GameObject), true);
        selectedCamera = (Camera)EditorGUILayout.ObjectField("相机对象", selectedCamera, typeof(Camera), true);

        GUILayout.Space(10);

        // 设置选项
        GUILayout.Label("2. 选择要设置的效果", EditorStyles.boldLabel);
        createPostProcessVolume = EditorGUILayout.Toggle("创建后处理Volume", createPostProcessVolume);
        setupCameraEnhancements = EditorGUILayout.Toggle("设置相机增强效果", setupCameraEnhancements);
        setupNitroVisualEffects = EditorGUILayout.Toggle("设置氮气视觉效果", setupNitroVisualEffects);
        setupSpeedLinesEffect = EditorGUILayout.Toggle("设置速度线条效果", setupSpeedLinesEffect);

        GUILayout.Space(10);

        // 自动检测按钮
        if (GUILayout.Button("自动检测车辆和相机"))
        {
            AutoDetectVehicleAndCamera();
        }

        GUILayout.Space(10);

        // 设置按钮
        GUI.enabled = selectedVehicle != null && selectedCamera != null;
        if (GUILayout.Button("开始设置氮气效果", GUILayout.Height(30)))
        {
            SetupNitroEffects();
        }
        GUI.enabled = true;

        GUILayout.Space(10);

        // 帮助信息
        EditorGUILayout.HelpBox(
            "此工具将为选定的车辆和相机设置氮气加速的视觉效果，包括：\n" +
            "• 镜头畸变和色彩增强\n" +
            "• 屏幕震动效果\n" +
            "• 氮气火焰和光效\n" +
            "• 速度线条效果\n" +
            "• 地面冲击波效果",
            MessageType.Info);

        GUILayout.Space(10);

        // 预制体创建按钮
        GUILayout.Label("3. 创建效果预制体", EditorStyles.boldLabel);
        if (GUILayout.Button("创建氮气火焰预制体"))
        {
            CreateNitroFlamePrefab();
        }
        if (GUILayout.Button("创建速度线条预制体"))
        {
            CreateSpeedLinesPrefab();
        }
        if (GUILayout.Button("创建地面冲击波预制体"))
        {
            CreateGroundShockwavePrefab();
        }
    }

    private void AutoDetectVehicleAndCamera()
    {
        // 查找带有Player标签的车辆
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player != null)
        {
            CarController carController = player.GetComponent<CarController>();
            if (carController != null)
            {
                selectedVehicle = player;
                Debug.Log("自动检测到车辆: " + player.name);
            }
        }

        // 查找主相机
        Camera mainCamera = Camera.main;
        if (mainCamera != null)
        {
            selectedCamera = mainCamera;
            Debug.Log("自动检测到相机: " + mainCamera.name);
        }
        else
        {
            // 查找VehicleCamera
            VehicleCamera vehicleCamera = FindObjectOfType<VehicleCamera>();
            if (vehicleCamera != null)
            {
                selectedCamera = vehicleCamera.GetComponent<Camera>();
                Debug.Log("自动检测到车辆相机: " + vehicleCamera.name);
            }
        }
    }

    private void SetupNitroEffects()
    {
        try
        {
            // 1. 设置后处理Volume
            if (createPostProcessVolume)
            {
                SetupPostProcessVolume();
            }

            // 2. 设置相机增强效果
            if (setupCameraEnhancements)
            {
                SetupCameraEnhancements();
            }

            // 3. 设置氮气视觉效果
            if (setupNitroVisualEffects)
            {
                SetupNitroVisualEffects();
            }

            // 4. 设置速度线条效果
            if (setupSpeedLinesEffect)
            {
                SetupSpeedLinesEffect();
            }

            EditorUtility.DisplayDialog("设置完成", "氮气效果设置完成！请检查各个组件的参数设置。", "确定");
        }
        catch (System.Exception e)
        {
            EditorUtility.DisplayDialog("设置失败", "设置过程中出现错误：\n" + e.Message, "确定");
            Debug.LogError("氮气效果设置失败: " + e.Message);
        }
    }

    private void SetupPostProcessVolume()
    {
        // 查找现有的Volume
        Volume existingVolume = FindObjectOfType<Volume>();
        
        if (existingVolume == null)
        {
            // 创建新的Volume
            GameObject volumeObject = new GameObject("Post Process Volume");
            Volume volume = volumeObject.AddComponent<Volume>();
            
            // 创建Volume Profile
            VolumeProfile profile = ScriptableObject.CreateInstance<VolumeProfile>();
            
            // 添加镜头畸变
            LensDistortion lensDistortion = profile.Add<LensDistortion>();
            lensDistortion.intensity.value = 0f;
            lensDistortion.active = true;
            
            // 添加色彩调整
            ColorAdjustments colorAdjustments = profile.Add<ColorAdjustments>();
            colorAdjustments.saturation.value = 0f;
            colorAdjustments.contrast.value = 0f;
            colorAdjustments.active = true;
            
            volume.profile = profile;
            volume.isGlobal = true;
            
            // 保存Profile
            string profilePath = "Assets/Settings/NitroEffectsProfile.asset";
            AssetDatabase.CreateAsset(profile, profilePath);
            AssetDatabase.SaveAssets();
            
            Debug.Log("创建了新的后处理Volume: " + volumeObject.name);
        }
        else
        {
            Debug.Log("使用现有的后处理Volume: " + existingVolume.name);
        }
    }

    private void SetupCameraEnhancements()
    {
        // 检查是否已有CameraFOVController
        CameraFOVController fovController = selectedCamera.GetComponent<CameraFOVController>();
        
        if (fovController == null)
        {
            fovController = selectedCamera.gameObject.AddComponent<CameraFOVController>();
            Debug.Log("添加了CameraFOVController组件到相机: " + selectedCamera.name);
        }

        // 设置后处理Volume引用
        Volume volume = FindObjectOfType<Volume>();
        if (volume != null)
        {
            SerializedObject serializedController = new SerializedObject(fovController);
            SerializedProperty volumeProperty = serializedController.FindProperty("postProcessVolume");
            volumeProperty.objectReferenceValue = volume;
            serializedController.ApplyModifiedProperties();
        }
    }

    private void SetupNitroVisualEffects()
    {
        // 检查是否已有NitroVisualEffectsManager
        NitroVisualEffectsManager effectsManager = selectedVehicle.GetComponent<NitroVisualEffectsManager>();
        
        if (effectsManager == null)
        {
            effectsManager = selectedVehicle.AddComponent<NitroVisualEffectsManager>();
            Debug.Log("添加了NitroVisualEffectsManager组件到车辆: " + selectedVehicle.name);
        }

        // 设置CarController引用
        CarController carController = selectedVehicle.GetComponent<CarController>();
        if (carController != null)
        {
            SerializedObject serializedManager = new SerializedObject(effectsManager);
            SerializedProperty carControllerProperty = serializedManager.FindProperty("carController");
            carControllerProperty.objectReferenceValue = carController;
            serializedManager.ApplyModifiedProperties();
        }

        // 创建氮气火焰位置点
        CreateNitroFlamePositions();
    }

    private void SetupSpeedLinesEffect()
    {
        // 检查是否已有SpeedLinesEffect
        SpeedLinesEffect speedLinesEffect = selectedCamera.GetComponent<SpeedLinesEffect>();
        
        if (speedLinesEffect == null)
        {
            speedLinesEffect = selectedCamera.gameObject.AddComponent<SpeedLinesEffect>();
            Debug.Log("添加了SpeedLinesEffect组件到相机: " + selectedCamera.name);
        }

        // 设置CarController引用
        CarController carController = selectedVehicle.GetComponent<CarController>();
        if (carController != null)
        {
            SerializedObject serializedEffect = new SerializedObject(speedLinesEffect);
            SerializedProperty carControllerProperty = serializedEffect.FindProperty("carController");
            carControllerProperty.objectReferenceValue = carController;
            serializedEffect.ApplyModifiedProperties();
        }
    }

    private void CreateNitroFlamePositions()
    {
        // 在车辆后部创建氮气火焰位置点
        Transform vehicleTransform = selectedVehicle.transform;
        
        // 查找现有的氮气位置点
        Transform existingPositions = vehicleTransform.Find("NitroFlamePositions");
        if (existingPositions == null)
        {
            GameObject positionsParent = new GameObject("NitroFlamePositions");
            positionsParent.transform.SetParent(vehicleTransform);
            positionsParent.transform.localPosition = Vector3.zero;
            
            // 创建左右两个氮气喷射点
            CreateFlamePosition(positionsParent.transform, "NitroFlameLeft", new Vector3(-0.5f, 0.2f, -2f));
            CreateFlamePosition(positionsParent.transform, "NitroFlameRight", new Vector3(0.5f, 0.2f, -2f));
            
            Debug.Log("创建了氮气火焰位置点");
        }
    }

    private void CreateFlamePosition(Transform parent, string name, Vector3 localPosition)
    {
        GameObject flamePosition = new GameObject(name);
        flamePosition.transform.SetParent(parent);
        flamePosition.transform.localPosition = localPosition;
        flamePosition.transform.localRotation = Quaternion.identity;
    }

    private void CreateNitroFlamePrefab()
    {
        // 创建氮气火焰粒子系统预制体
        GameObject flameObject = new GameObject("NitroFlame");
        ParticleSystem particles = flameObject.AddComponent<ParticleSystem>();
        
        // 配置粒子系统
        var main = particles.main;
        main.startLifetime = 0.3f;
        main.startSpeed = 15f;
        main.startSize = 0.3f;
        main.startColor = Color.cyan;
        main.maxParticles = 100;
        
        var emission = particles.emission;
        emission.rateOverTime = 80f;
        
        var shape = particles.shape;
        shape.shapeType = ParticleSystemShapeType.Cone;
        shape.angle = 10f;
        
        // 保存为预制体
        string prefabPath = "Assets/Prefabs/Effects/NitroFlame.prefab";
        PrefabUtility.SaveAsPrefabAsset(flameObject, prefabPath);
        DestroyImmediate(flameObject);
        
        Debug.Log("创建了氮气火焰预制体: " + prefabPath);
    }

    private void CreateSpeedLinesPrefab()
    {
        // 创建速度线条粒子系统预制体
        GameObject speedLinesObject = new GameObject("SpeedLines");
        ParticleSystem particles = speedLinesObject.AddComponent<ParticleSystem>();
        
        // 配置粒子系统
        var main = particles.main;
        main.startLifetime = 1f;
        main.startSpeed = 20f;
        main.startSize = 0.1f;
        main.startColor = Color.white;
        main.maxParticles = 200;
        
        var emission = particles.emission;
        emission.rateOverTime = 50f;
        
        var shape = particles.shape;
        shape.shapeType = ParticleSystemShapeType.Circle;
        shape.radius = 10f;
        
        // 保存为预制体
        string prefabPath = "Assets/Prefabs/Effects/SpeedLines.prefab";
        PrefabUtility.SaveAsPrefabAsset(speedLinesObject, prefabPath);
        DestroyImmediate(speedLinesObject);
        
        Debug.Log("创建了速度线条预制体: " + prefabPath);
    }

    private void CreateGroundShockwavePrefab()
    {
        // 创建地面冲击波粒子系统预制体
        GameObject shockwaveObject = new GameObject("GroundShockwave");
        ParticleSystem particles = shockwaveObject.AddComponent<ParticleSystem>();
        
        // 配置粒子系统
        var main = particles.main;
        main.startLifetime = 2f;
        main.startSpeed = 5f;
        main.startSize = 1f;
        main.startColor = Color.gray;
        main.maxParticles = 50;
        
        var emission = particles.emission;
        emission.SetBursts(new ParticleSystem.Burst[] {
            new ParticleSystem.Burst(0f, 30)
        });
        
        var shape = particles.shape;
        shape.shapeType = ParticleSystemShapeType.Circle;
        shape.radius = 2f;
        
        // 保存为预制体
        string prefabPath = "Assets/Prefabs/Effects/GroundShockwave.prefab";
        PrefabUtility.SaveAsPrefabAsset(shockwaveObject, prefabPath);
        DestroyImmediate(shockwaveObject);
        
        Debug.Log("创建了地面冲击波预制体: " + prefabPath);
    }
}
