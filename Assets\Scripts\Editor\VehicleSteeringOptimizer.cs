using UnityEngine;
using UnityEditor;
using System.IO;

/// <summary>
/// 编辑器工具：批量优化车辆转向参数，使其更适合街机赛车游戏
/// </summary>
public class VehicleSteeringOptimizer : EditorWindow
{
    [MenuItem("Tools/Vehicle/Optimize Steering for Arcade Racing")]
    public static void ShowWindow()
    {
        GetWindow<VehicleSteeringOptimizer>("Vehicle Steering Optimizer");
    }

    private void OnGUI()
    {
        GUILayout.Label("车辆转向系统优化工具", EditorStyles.boldLabel);
        GUILayout.Space(10);

        GUILayout.Label("此工具将优化所有车辆数据的转向参数，使其更适合街机赛车游戏：", EditorStyles.wordWrappedLabel);
        GUILayout.Label("• 增加基础转向角度到 35-40 度", EditorStyles.miniLabel);
        GUILayout.Label("• 优化转向敏感度曲线，提供更平缓的衰减", EditorStyles.miniLabel);
        GUILayout.Label("• 保持高速时的转向能力", EditorStyles.miniLabel);
        
        GUILayout.Space(20);

        if (GUILayout.Button("优化所有车辆转向参数", GUILayout.Height(30)))
        {
            OptimizeAllVehiclesSteering();
        }

        GUILayout.Space(10);

        if (GUILayout.Button("恢复默认转向参数", GUILayout.Height(25)))
        {
            RestoreDefaultSteering();
        }
    }

    private void OptimizeAllVehiclesSteering()
    {
        string[] vehicleGuids = AssetDatabase.FindAssets("t:VehicleData");
        int optimizedCount = 0;

        foreach (string guid in vehicleGuids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            VehicleData vehicleData = AssetDatabase.LoadAssetAtPath<VehicleData>(path);

            if (vehicleData != null)
            {
                // 记录原始值
                float originalSteeringAngle = vehicleData.m_BaseMaxSteeringAngle;
                
                // 优化转向角度
                vehicleData.m_BaseMaxSteeringAngle = Mathf.Max(35f, vehicleData.m_BaseMaxSteeringAngle);
                
                // 创建街机风格的转向敏感度曲线
                AnimationCurve arcadeCurve = new AnimationCurve();
                arcadeCurve.AddKey(0f, 1.0f);      // 0 km/h: 100% 转向
                arcadeCurve.AddKey(30f, 0.9f);     // 30 km/h: 90% 转向
                arcadeCurve.AddKey(60f, 0.7f);     // 60 km/h: 70% 转向
                arcadeCurve.AddKey(100f, 0.5f);    // 100 km/h: 50% 转向
                arcadeCurve.AddKey(150f, 0.4f);    // 150 km/h: 40% 转向
                
                // 设置曲线的切线为平滑
                for (int i = 0; i < arcadeCurve.length; i++)
                {
                    AnimationUtility.SetKeyLeftTangentMode(arcadeCurve, i, AnimationUtility.TangentMode.Auto);
                    AnimationUtility.SetKeyRightTangentMode(arcadeCurve, i, AnimationUtility.TangentMode.Auto);
                }
                
                vehicleData.m_SteeringSensitivityCurve = arcadeCurve;
                
                // 标记为已修改
                EditorUtility.SetDirty(vehicleData);
                optimizedCount++;
                
                Debug.Log($"优化车辆 '{vehicleData.m_VehicleName}': 转向角度 {originalSteeringAngle}° → {vehicleData.m_BaseMaxSteeringAngle}°");
            }
        }

        // 保存所有修改
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        EditorUtility.DisplayDialog("优化完成", 
            $"成功优化了 {optimizedCount} 个车辆的转向参数。\n\n" +
            "所有车辆现在都具有更适合街机赛车游戏的转向特性。", "确定");
    }

    private void RestoreDefaultSteering()
    {
        if (!EditorUtility.DisplayDialog("确认恢复", 
            "这将恢复所有车辆的默认转向参数。此操作无法撤销。\n\n确定要继续吗？", "确定", "取消"))
        {
            return;
        }

        string[] vehicleGuids = AssetDatabase.FindAssets("t:VehicleData");
        int restoredCount = 0;

        foreach (string guid in vehicleGuids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            VehicleData vehicleData = AssetDatabase.LoadAssetAtPath<VehicleData>(path);

            if (vehicleData != null)
            {
                // 恢复默认转向角度
                vehicleData.m_BaseMaxSteeringAngle = 30f;
                
                // 恢复默认转向敏感度曲线
                vehicleData.m_SteeringSensitivityCurve = AnimationCurve.Linear(0f, 1f, 180f, 0.3f);
                
                // 标记为已修改
                EditorUtility.SetDirty(vehicleData);
                restoredCount++;
            }
        }

        // 保存所有修改
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        EditorUtility.DisplayDialog("恢复完成", 
            $"成功恢复了 {restoredCount} 个车辆的默认转向参数。", "确定");
    }
}
