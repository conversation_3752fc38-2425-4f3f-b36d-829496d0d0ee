# 车辆加速度平衡指南

## 问题描述

当前车辆系统存在加速过快的问题：
- 车辆在1-2秒内就能达到最大速度
- 缺乏真实的加速感和渐进性
- 影响游戏体验和操控感

## 问题根源分析

### 1. 扭矩参数过高
```
sedan: m_BaseMaxMotorTorque = 5000 (过高)
Police: m_BaseMaxMotorTorque = 4000 (过高)
```
**问题**: 对于1400kg的车辆，5000的扭矩过于强大

### 2. 功率衰减起始点过晚
```
sedan: m_BaseEnginePowerFalloffStartFactor = 0.95
Police: m_BaseEnginePowerFalloffStartFactor = 0.85
```
**问题**: 在95%目标速度才开始衰减，意味着大部分速度范围都是全功率

### 3. 扭矩曲线不合理
```
当前曲线: 0%速度时扭矩倍数为1.2 (不现实)
```
**问题**: 低速时反而有扭矩加成，违背物理规律

### 4. 目标引擎速度设置不当
```
sedan: 目标90km/h，最大130km/h (差距过小)
```
**问题**: 最佳性能区间过窄

## 解决方案

### 🛠️ 方案一：使用自动化工具（推荐）

1. 打开 `Tools > Vehicle > Balance Vehicle Acceleration`
2. 使用推荐设置或自定义参数
3. 点击"应用平衡调整"

**推荐参数**:
- 扭矩减少因子: 0.55 (减少到原来的55%)
- 功率衰减起始点: 0.75 (75%目标速度开始衰减)
- 目标速度调整因子: 0.85 (调整到原来的85%)

### 🔧 方案二：手动调整参数

#### 1. 降低基础扭矩 (最重要)
```
sedan: 5000 → 2750
Police: 4000 → 2200
Ambulance: 建议 2500
Minivan: 建议 2300
Taxi: 建议 2600
```

#### 2. 提前功率衰减起始点
```
所有车辆: m_BaseEnginePowerFalloffStartFactor → 0.75
```
这样车辆在75%目标速度时就开始功率衰减

#### 3. 调整目标引擎速度
```
sedan: 90 → 75 km/h
Police: 80 → 68 km/h
其他车辆: 按比例减少15%
```

#### 4. 优化扭矩曲线
创建更真实的扭矩分布：
```
0%速度: 1.0 (起步正常扭矩)
25%速度: 1.15 (峰值扭矩区间)
50%速度: 1.0 (正常扭矩)
75%速度: 0.85 (扭矩开始下降)
100%速度: 0.7 (高速低扭矩)
```

## 参数详解

### m_BaseMaxMotorTorque (基础最大扭矩)
- **作用**: 决定车辆的基础加速能力
- **当前问题**: 数值过高导致加速过快
- **建议范围**: 2000-3000 (根据车辆类型)
- **调整效果**: 直接影响加速度

### m_BaseEnginePowerFalloffStartFactor (功率衰减起始因子)
- **作用**: 决定何时开始功率衰减
- **当前问题**: 0.95意味着95%速度才衰减
- **建议值**: 0.7-0.8
- **调整效果**: 提供更真实的加速曲线

### m_TargetEngineSpeedKPH (目标引擎速度)
- **作用**: 引擎最佳性能的速度范围
- **当前问题**: 与最大速度差距过小
- **建议**: 最大速度的60-70%
- **调整效果**: 影响最佳性能区间

### m_DefaultEngineTorqueCurve (引擎扭矩曲线)
- **作用**: 不同速度下的扭矩倍数
- **当前问题**: 低速时扭矩过高
- **建议**: 25%速度处达到峰值
- **调整效果**: 影响整个加速过程的感觉

## 不同车型的推荐设置

### 轿车 (Sedan)
```
m_BaseMaxMotorTorque: 2750
m_BaseEnginePowerFalloffStartFactor: 0.75
m_TargetEngineSpeedKPH: 75
扭矩曲线: 平衡型
```

### 警车 (Police)
```
m_BaseMaxMotorTorque: 2200
m_BaseEnginePowerFalloffStartFactor: 0.72
m_TargetEngineSpeedKPH: 68
扭矩曲线: 高速优化型
```

### 救护车 (Ambulance)
```
m_BaseMaxMotorTorque: 2500
m_BaseEnginePowerFalloffStartFactor: 0.78
m_TargetEngineSpeedKPH: 70
扭矩曲线: 稳定型
```

### 面包车 (Minivan)
```
m_BaseMaxMotorTorque: 2300
m_BaseEnginePowerFalloffStartFactor: 0.8
m_TargetEngineSpeedKPH: 65
扭矩曲线: 低速优化型
```

## 测试建议

### 1. 加速测试
- 从静止加速到最大速度应该需要4-6秒
- 加速过程应该有明显的阶段性
- 高速时加速应该明显变慢

### 2. 爬坡测试
- 确保车辆在坡道上仍有足够动力
- 爬坡时速度下降应该合理
- 不应该出现倒滑现象

### 3. 超车测试
- 中速时应该还有超车能力
- 超车加速应该比起步加速慢
- 接近最大速度时超车能力有限

## 微调指南

### 如果加速还是太快
1. 进一步降低 `m_BaseMaxMotorTorque` (减少10-20%)
2. 提前 `m_BaseEnginePowerFalloffStartFactor` (降低到0.65-0.7)
3. 调整扭矩曲线，降低峰值

### 如果加速太慢
1. 适当增加 `m_BaseMaxMotorTorque` (增加10-15%)
2. 延后 `m_BaseEnginePowerFalloffStartFactor` (提高到0.8-0.85)
3. 检查扭矩曲线是否过于保守

### 如果爬坡能力不足
1. 优化扭矩曲线，增加低速扭矩
2. 适当增加基础扭矩
3. 检查车辆质量设置

## 注意事项

1. **平衡性**: 不同车型应该有不同的特性
2. **一致性**: 同类型车辆的参数应该相近
3. **测试**: 每次调整后都要进行全面测试
4. **备份**: 调整前备份原始参数

## 恢复原始设置

如果调整后效果不理想，可以：
1. 使用工具中的"恢复原始参数"功能
2. 或者手动恢复到调整前的数值

---

**提示**: 建议先使用自动化工具进行批量调整，然后根据测试结果对个别车辆进行微调。记住，好的加速感应该是渐进的，而不是瞬间的！
