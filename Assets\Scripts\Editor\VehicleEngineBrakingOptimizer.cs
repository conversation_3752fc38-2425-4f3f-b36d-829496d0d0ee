using UnityEngine;
using UnityEditor;

/// <summary>
/// 编辑器工具：优化车辆引擎制动和阻力参数
/// </summary>
public class VehicleEngineBrakingOptimizer : EditorWindow
{
    [MenuItem("Tools/Vehicle/Optimize Engine Braking System")]
    public static void ShowWindow()
    {
        GetWindow<VehicleEngineBrakingOptimizer>("Engine Braking Optimizer");
    }

    private float engineBrakingStrength = 0.8f;
    private float engineBrakingMinSpeed = 10f;
    private float speedBasedDragFactor = 0.3f;
    private float baseDragMultiplier = 1.5f;

    private void OnGUI()
    {
        GUILayout.Label("车辆引擎制动优化工具", EditorStyles.boldLabel);
        GUILayout.Space(10);

        GUILayout.Label("当前问题：松开油门后车辆速度几乎不衰减", EditorStyles.wordWrappedLabel);
        GUILayout.Label("解决方案：添加引擎制动系统和动态阻力", EditorStyles.wordWrappedLabel);
        
        GUILayout.Space(15);
        GUILayout.Label("引擎制动参数", EditorStyles.boldLabel);
        
        engineBrakingStrength = EditorGUILayout.Slider("引擎制动强度", engineBrakingStrength, 0.3f, 1.5f);
        GUILayout.Label("控制松开油门时的减速力度", EditorStyles.miniLabel);
        
        engineBrakingMinSpeed = EditorGUILayout.Slider("引擎制动最小速度 (km/h)", engineBrakingMinSpeed, 5f, 25f);
        GUILayout.Label("低于此速度时引擎制动减弱，避免突然停止", EditorStyles.miniLabel);
        
        speedBasedDragFactor = EditorGUILayout.Slider("速度相关阻力系数", speedBasedDragFactor, 0.1f, 0.8f);
        GUILayout.Label("高速时增加额外阻力", EditorStyles.miniLabel);
        
        GUILayout.Space(10);
        GUILayout.Label("阻力优化", EditorStyles.boldLabel);
        
        baseDragMultiplier = EditorGUILayout.Slider("基础阻力倍数", baseDragMultiplier, 1f, 3f);
        GUILayout.Label("增加车辆数据中的基础阻力值", EditorStyles.miniLabel);
        
        GUILayout.Space(20);

        if (GUILayout.Button("应用引擎制动优化", GUILayout.Height(30)))
        {
            ApplyEngineBrakingOptimization();
        }

        GUILayout.Space(10);

        if (GUILayout.Button("恢复原始参数", GUILayout.Height(25)))
        {
            RestoreOriginalParameters();
        }

        GUILayout.Space(15);
        GUILayout.Label("推荐设置", EditorStyles.boldLabel);
        if (GUILayout.Button("应用推荐的引擎制动设置"))
        {
            engineBrakingStrength = 0.8f;
            engineBrakingMinSpeed = 10f;
            speedBasedDragFactor = 0.3f;
            baseDragMultiplier = 1.8f;
            ApplyEngineBrakingOptimization();
        }
        
        GUILayout.Space(10);
        GUILayout.Label("说明：", EditorStyles.boldLabel);
        GUILayout.Label("• 引擎制动：松开油门时的自然减速", EditorStyles.miniLabel);
        GUILayout.Label("• 动态阻力：速度越高阻力越大", EditorStyles.miniLabel);
        GUILayout.Label("• 低速保护：避免车辆突然停止", EditorStyles.miniLabel);
        GUILayout.Label("• 基础阻力：增加车辆数据中的阻力值", EditorStyles.miniLabel);
    }

    private void ApplyEngineBrakingOptimization()
    {
        // 1. 优化所有场景中的CarController组件
        OptimizeCarControllers();
        
        // 2. 优化VehicleData中的阻力参数
        OptimizeVehicleDataDrag();
        
        EditorUtility.DisplayDialog("优化完成", 
            "引擎制动系统优化完成！\n\n" +
            "主要改进：\n" +
            $"• 引擎制动强度: {engineBrakingStrength:F1}\n" +
            $"• 引擎制动最小速度: {engineBrakingMinSpeed:F0} km/h\n" +
            $"• 速度相关阻力系数: {speedBasedDragFactor:F1}\n" +
            $"• 基础阻力增加: {baseDragMultiplier:F1}x\n\n" +
            "现在松开油门后车辆会自然减速！", "确定");
    }

    private void OptimizeCarControllers()
    {
        // 查找场景中所有的CarController
        CarController[] carControllers = FindObjectsOfType<CarController>();
        int optimizedCount = 0;

        foreach (CarController carController in carControllers)
        {
            carController.engineBrakingStrength = engineBrakingStrength;
            carController.engineBrakingMinSpeedKPH = engineBrakingMinSpeed;
            carController.speedBasedDragFactor = speedBasedDragFactor;
            
            // 标记为已修改
            EditorUtility.SetDirty(carController);
            optimizedCount++;
        }

        Debug.Log($"优化了 {optimizedCount} 个场景中的CarController组件");
    }

    private void OptimizeVehicleDataDrag()
    {
        string[] vehicleGuids = AssetDatabase.FindAssets("t:VehicleData");
        int optimizedCount = 0;

        foreach (string guid in vehicleGuids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            VehicleData vehicleData = AssetDatabase.LoadAssetAtPath<VehicleData>(path);

            if (vehicleData != null)
            {
                // 记录原始值
                float originalDrag = vehicleData.m_BaseDrag;
                
                // 增加基础阻力
                vehicleData.m_BaseDrag *= baseDragMultiplier;
                
                // 确保阻力值在合理范围内
                vehicleData.m_BaseDrag = Mathf.Clamp(vehicleData.m_BaseDrag, 0.1f, 1.0f);
                
                // 标记为已修改
                EditorUtility.SetDirty(vehicleData);
                optimizedCount++;
                
                Debug.Log($"优化车辆 '{vehicleData.m_VehicleName}': 阻力 {originalDrag:F2} → {vehicleData.m_BaseDrag:F2}");
            }
        }

        // 保存所有修改
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        Debug.Log($"优化了 {optimizedCount} 个车辆数据的阻力参数");
    }

    private void RestoreOriginalParameters()
    {
        if (!EditorUtility.DisplayDialog("确认恢复", 
            "这将恢复所有车辆的原始引擎制动和阻力参数。\n\n" +
            "注意：这会让车辆重新变得松开油门后不减速！\n\n确定要继续吗？", "确定", "取消"))
        {
            return;
        }

        // 恢复CarController参数
        CarController[] carControllers = FindObjectsOfType<CarController>();
        foreach (CarController carController in carControllers)
        {
            carController.engineBrakingStrength = 0f; // 禁用引擎制动
            carController.engineBrakingMinSpeedKPH = 10f;
            carController.speedBasedDragFactor = 0f; // 禁用速度相关阻力
            EditorUtility.SetDirty(carController);
        }

        // 恢复VehicleData阻力参数
        string[] vehicleGuids = AssetDatabase.FindAssets("t:VehicleData");
        foreach (string guid in vehicleGuids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            VehicleData vehicleData = AssetDatabase.LoadAssetAtPath<VehicleData>(path);

            if (vehicleData != null)
            {
                // 恢复原始阻力值（根据车辆类型）
                if (vehicleData.m_VehicleName.Contains("sedan") || vehicleData.m_VehicleName.Contains("轿车"))
                {
                    vehicleData.m_BaseDrag = 0.2f;
                }
                else if (vehicleData.m_VehicleName.Contains("Police") || vehicleData.m_VehicleName.Contains("警车"))
                {
                    vehicleData.m_BaseDrag = 0.18f;
                }
                else
                {
                    vehicleData.m_BaseDrag = 0.2f; // 默认值
                }
                
                EditorUtility.SetDirty(vehicleData);
            }
        }

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        EditorUtility.DisplayDialog("恢复完成", 
            "成功恢复了所有车辆的原始引擎制动和阻力参数。", "确定");
    }
}
